'use client';
import { <PERSON>readcrumbItem, Breadcrumbs, Chip, Modal, ModalContent, ModalBody, useDisclosure,  } from '@heroui/react';
import { useState } from 'react';
import { Button, Tooltip, Select, SelectItem } from '@heroui/react';
import Image from 'next/image';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';

import { InfoIcon } from '@/components/icons';
import { recommendations } from '@/types/recommendationsData';
import BookThisPlan from '@/components/globalComponents/BookThisPlan';

type Props = {
  location: string;
  title: string;
};

function decodeSlug(text?: string) {
  return text ? text.toLowerCase().replace(/\s/g, '-') : '';
}
export default function PhotoExpenseCard({ location, title }: Props) {
  const [currency, setCurrency] = useState('$');
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Find matching recommendation
  const recommendation = recommendations.find(
    item =>
      decodeSlug(item.location) === decodeSlug(location) &&
      decodeSlug(item.title) === decodeSlug(title)
  );
  if (!recommendation) {
    return (
      <p className="text-red-500">
        No data found for "{title} {location}"
      </p>
    );
  }

  const images = [
    { src: recommendation.media.large, alt: 'Large Image' },
    ...recommendation.media.small.map((src, i) => ({
      src,
      alt: `Small Image ${i + 1}`,
    })),
  ];

  const handleViewMore = () => {
    setLightboxIndex(0);
    setLightboxOpen(true);
  };


  
  // Modal state for Book This Plan
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();

    // Function to handle opening the Book This Plan modal
  const handleBookThisPlanClick = () => {
    onOpen();
  };
  return (
    <div className="max-w-6xl mx-auto rounded-2xl overflow-hidden mb-4">
      <div className="flex flex-col md:flex-row gap-2">
        {/* Left side images */}
        <div className="flex flex-col md:flex-row md:w-2/3 gap-2">
          {/* Large Image */}
          <div className="md:w-2/3">
            <Image
              src={recommendation.media.large}
              alt="Large Image"
              width={800}
              height={300}
              className="w-full h-full object-cover rounded-none md:rounded-l-xl"
            />
          </div>

          {/* Two Small Images */}
          <div className="md:w-1/3 flex flex-col gap-2">
            {recommendation.media.small.slice(0, 1).map((src, index) => (
              <Image
                key={index}
                src={src}
                alt={`Small Image ${index + 1}`}
                width={400}
                height={200}
                className="w-full h-[150px] object-cover rounded-tr-xl"
              />
            ))}

            {/* Blurred last image */}
            <div className="relative">
              <Image
                src={recommendation.media.small[1]}
                alt="Small Image 2"
                width={400}
                height={200}
                className="w-full h-[150px] object-cover rounded-br-xl blur-[2px]"
              />
              <Button
                size="sm"
                className="absolute bottom-2 right-2 bg-white text-black text-xs px-3 py-1 rounded-full shadow-md hover:bg-gray-100"
                variant="flat"
                onPress={handleViewMore}
              >
                View more
              </Button>
            </div>
          </div>
        </div>

        {/* Expense Card */}
        <div className="md:w-1/3 bg-white rounded-xl flex flex-col p-4 gap-6 justify-between">
          <div>
            <div className="text-center w-full">
              <div className="text-lg font-semibold mb-4 flex items-center gap-1">
                Expense Estimate
                <Tooltip content="I am a tooltip">
                  <div className="cursor-pointer text-gray-500 hover:text-gray-700">
                    <InfoIcon size={18} />
                  </div>
                </Tooltip>
              </div>

              <div className="flex justify-between items-center gap-3 bg-[#F1F3F5] rounded-lg px-4 w-full py-2 mx-auto">
                <Select
                  value={currency}
                  className="w-[60px] text-sm"
                  onChange={e => setCurrency(e.target.value)}
                  size="sm"
                >
                  <SelectItem key="$">$</SelectItem>
                  <SelectItem key="€">€</SelectItem>
                  <SelectItem key="₹">₹</SelectItem>
                </Select>
                <span className="text-xl font-semibold text-gray-800">
                  999.00
                </span>
              </div>
            </div>

            <p className="text-sm text-gray-500 text-center mt-3">
              Please login to customise expense
            </p>
            <div>
              <div className="flex justify-between items-center gap-3 w-full">
                <p className="text-sm text-gray-500 text-center mt-2">
                  Expense Estimate
                </p>
                <p>{currency} 120.00</p>
              </div>
              <div className="flex justify-between items-center gap-3 w-full">
                <p className="text-sm text-gray-500 text-center mt-1">
                  Transport
                </p>
                <p>{currency} 120.00</p>
              </div>
              <div className="flex justify-between items-center gap-3 w-full">
                <p className="text-sm text-gray-500 text-center mt-1">Hotel</p>
                <p>{currency} 120.00</p>
              </div>
            </div>
          </div>

          <Button
            size="lg"
            onPress={handleBookThisPlanClick}
            className="w-full max-w-sm rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold shadow-md hover:opacity-90 transition"
          >
            Get Started
          </Button>
        </div>
      </div>

      {/* Lightbox Component */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={images}
        index={lightboxIndex}
        on={{
          view: ({ index }) => setLightboxIndex(index),
        }}
      />
            {/* Full Screen Book This Plan Modal */}
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="full"
        classNames={{
          base: "m-0 max-w-none max-h-none",
          wrapper: "w-full h-full",
          body: "p-0",
        }}
        hideCloseButton={true}
        isDismissable={true}
        isKeyboardDismissDisabled={false}
      >
        <ModalContent className="h-full">
          <ModalBody className="h-full p-0">
            <BookThisPlan onClose={onClose} />
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
}
